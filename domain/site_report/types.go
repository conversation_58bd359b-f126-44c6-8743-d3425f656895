package sitereport

import (
	"time"

	basicprice "github.com/Sera-Global/be-nbs-accounting-system/domain/basic_price"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/block"
	dailyreportaddition "github.com/Sera-Global/be-nbs-accounting-system/domain/daily_report_addition"
	departmentpic "github.com/Sera-Global/be-nbs-accounting-system/domain/department_pic"
	sitereportoption "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_option"
	sitereportstatutory "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_statutory"
	sitereportworker "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
)

type SiteReport struct {
	ID                    int64            `gorm:"column:id;primary_key"`
	WorkDate              time.Time        `gorm:"column:work_date"`
	SiteName              string           `gorm:"column:site_name"`
	DepartmentPicID       int64            `gorm:"column:department_pic_id"`
	HasStatutory          bool             `gorm:"column:has_statutory"`
	BillDate              time.Time        `gorm:"column:bill_date"`
	BasicPriceID          int64            `gorm:"column:basic_price_id"`
	DailyReportAdditionID *int64           `gorm:"column:daily_report_addition_id"`
	Worker                int64            `gorm:"column:worker"`
	DistrictBlockID       int64            `gorm:"column:district_block_id"`
	DistrictBlockUnit     int64            `gorm:"column:district_block_unit"`
	TransitPlaceBlockID   *int64           `gorm:"column:transit_place_block_id"`
	TransitPlaceUnit      *int64           `gorm:"column:transit_place_unit"`
	BStartTime            utils.LocalTime  `gorm:"column:b_start_time"`
	BEndTime              utils.LocalTime  `gorm:"column:b_end_time"`
	SStartTime            utils.LocalTime  `gorm:"column:s_start_time"`
	SEndTime              utils.LocalTime  `gorm:"column:s_end_time"`
	BreakTime             *utils.LocalTime `gorm:"column:break_time"`
	LateEarlyWorker       int64            `gorm:"column:late_early_worker"`
	ExtraTimeCharge       string           `gorm:"column:extra_time_charge;type:json;default:'[]'"`
	Note                  string           `gorm:"column:note"`
	NoteForInvoice        string           `gorm:"column:note_for_invoice"`
	TotalAmount           float64          `gorm:"column:total_amount"`
	Snapshot              string           `gorm:"column:snapshot;type:json;default:'{}'"`
	IsLocked              bool             `gorm:"column:is_locked"`
	IsInvoiceIssued       bool             `gorm:"column:is_invoice_issued"`
	CreatedBy             int64            `gorm:"column:created_by"`
	CreatedAt             time.Time        `gorm:"column:created_at"`
	UpdatedAt             time.Time        `gorm:"column:updated_at"`
	DeletedAt             *time.Time       `gorm:"column:deleted_at"`

	// Foreign key relationships for GORM preloading
	DepartmentPic       departmentpic.DepartmentPic             `gorm:"foreignkey:DepartmentPicID"`
	BasicPrice          basicprice.BasicPrice                   `gorm:"foreignkey:BasicPriceID"`
	DailyReportAddition dailyreportaddition.DailyReportAddition `gorm:"foreignkey:DailyReportAdditionID"`
	DistrictBlock       block.Block                             `gorm:"foreignkey:DistrictBlockID"`
	TransitPlaceBlock   block.Block                             `gorm:"foreignkey:TransitPlaceBlockID"`

	// Nested object relationships (one-to-many)
	Statutory []sitereportstatutory.SiteReportStatutory `gorm:"foreignkey:SiteReportID"`
	Options   []sitereportoption.SiteReportOption       `gorm:"foreignkey:SiteReportID"`
	Workers   []sitereportworker.SiteReportWorker       `gorm:"foreignkey:SiteReportID"`
}

type Snapshot struct {
	CustomerID                   int64   `json:"customer_id"`
	CustomerName                 string  `json:"customer_name"`
	DepartmentID                 int64   `json:"department_id"`
	DepartmentName               string  `json:"department_name"`
	DepartmentPicName            string  `json:"department_pic_name"`
	BasicPriceName               string  `json:"basic_price_name"`
	DailyReportAdditionName      string  `json:"daily_report_addition_name"`
	DailyReportAdditionPerSite   float64 `json:"daily_report_addition_per_site"`
	DailyReportAdditionPerWorker float64 `json:"daily_report_addition_per_worker"`
	DistrictBlockName            string  `json:"district_block_name"`
	TransitPlaceBlockName        string  `json:"transit_place_block_name"`
}

type ExtraTimeChargeItem struct {
	Time        string `json:"time"`
	TotalWorker int64  `json:"total_worker"`
}

// GetListParam represents the parameters for getting site report list
type GetListParam struct {
	StartDate time.Time
	EndDate   time.Time
}

// BulkUpdateParam represents the parameters for bulk updating site reports
type BulkUpdateParam struct {
	SiteReportIDs   []int64
	WorkDate        *time.Time
	IsLocked        *bool
	IsInvoiceIssued *bool
}

// GetDetailListParam represents the parameters for getting site report detail list
type GetDetailListParam struct {
	WorkDate  *time.Time
	ID        *int64
	IDs       []int64
	IsPreload bool
}

// GetInvoiceListParam represents the parameters for getting site report invoice list
type GetInvoiceListParam struct {
	StartDate time.Time
	EndDate   time.Time
}

// CreateSiteReportParam represents the parameters for creating site report
type CreateSiteReportParam struct {
	WorkDate              time.Time
	SiteName              string
	DepartmentPicID       int64
	HasStatutory          bool
	BillDate              time.Time
	BasicPriceID          int64
	DailyReportAdditionID *int64
	Worker                int64
	DistrictBlockID       int64
	DistrictBlockUnit     int64
	TransitPlaceBlockID   *int64
	TransitPlaceUnit      *int64
	BStartTime            utils.LocalTime
	BEndTime              utils.LocalTime
	SStartTime            utils.LocalTime
	SEndTime              utils.LocalTime
	BreakTime             *utils.LocalTime
	LateEarlyWorker       int64
	ExtraTimeCharge       string
	Note                  string
	NoteForInvoice        string
	TotalAmount           float64
	Snapshot              string
	IsLocked              bool
	IsInvoiceIssued       bool
	CreatedBy             int64
	CreatedAt             time.Time
	UpdatedAt             time.Time
}

// UpdateSiteReportParam represents the parameters for updating site report
type UpdateSiteReportParam struct {
	ID                    int64
	WorkDate              time.Time
	SiteName              string
	DepartmentPicID       int64
	HasStatutory          bool
	BillDate              time.Time
	BasicPriceID          int64
	DailyReportAdditionID *int64
	Worker                int64
	DistrictBlockID       int64
	DistrictBlockUnit     int64
	TransitPlaceBlockID   *int64
	TransitPlaceUnit      *int64
	BStartTime            utils.LocalTime
	BEndTime              utils.LocalTime
	SStartTime            utils.LocalTime
	SEndTime              utils.LocalTime
	BreakTime             *utils.LocalTime
	LateEarlyWorker       int64
	ExtraTimeCharge       string
	Note                  string
	NoteForInvoice        string
	TotalAmount           float64
	Snapshot              string
	UpdatedAt             time.Time
}

// DeleteSiteReportParam represents the parameters for deleting site report
type DeleteSiteReportParam struct {
	ID        int64
	DeletedAt time.Time
}
