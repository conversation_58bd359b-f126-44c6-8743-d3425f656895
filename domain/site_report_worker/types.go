package sitereportworker

import (
	"time"

	distantfeeDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/distant_fee"
	sitereportworkerqualificationDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report_worker_qualification"
	userDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/user"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
)

type SiteReportWorker struct {
	ID                    int64            `gorm:"column:id;primary_key"`
	SiteReportID          int64            `gorm:"column:site_report_id"`
	UserID                int64            `gorm:"column:user_id"`
	StartTime             utils.LocalTime  `gorm:"column:start_time;type:time"`
	EndTime               utils.LocalTime  `gorm:"column:end_time;type:time"`
	BreakTime             *utils.LocalTime `gorm:"column:break_time;type:time"`
	TransportationExpense float64          `gorm:"column:transportation_expense"`
	LeaderAllowance       float64          `gorm:"column:leader_allowance"`
	DistantFeeID          *int64           `gorm:"column:distant_fee_id"`
	IncomeTaxID           int64            `gorm:"column:income_tax_id"`
	Tax                   float64          `gorm:"column:tax"`
	Amount                float64          `gorm:"column:amount"`
	Snapshot              string           `gorm:"column:snapshot;type:json"`
	Status                string           `gorm:"column:status"`
	IssuedDate            *time.Time       `gorm:"column:issued_date;type:date"`

	// Foreign key relationship for user
	User       userDmn.User             `gorm:"foreignkey:UserID"`
	DistantFee distantfeeDmn.DistantFee `gorm:"foreignkey:DistantFeeID"`

	Qualifications []sitereportworkerqualificationDmn.SiteReportWorkerQualification `gorm:"foreignkey:SiteReportWorkerID"`
}

type Snapshot struct {
	WorkerName     string  `json:"worker_name"`
	DistantFee     float64 `json:"distant_fee"`
	DistantFeeName string  `json:"distant_fee_name"`
}

// CreateWorkerParam represents the parameters for creating site report worker
type CreateWorkerParam struct {
	SiteReportID          int64
	UserID                int64
	StartTime             time.Time
	EndTime               time.Time
	BreakTime             *time.Time
	TransportationExpense float64
	LeaderAllowance       float64
	DistantFeeID          *int64
	IncomeTaxID           int64
	Tax                   float64
	Amount                float64
	Snapshot              string
	Status                string
	IssuedDate            *time.Time
}

// UpdateWorkerParam represents the parameters for updating site report worker
type UpdateWorkerParam struct {
	ID                    int64
	UserID                int64
	StartTime             time.Time
	EndTime               time.Time
	BreakTime             *time.Time
	TransportationExpense float64
	LeaderAllowance       float64
	DistantFeeID          *int64
	IncomeTaxID           int64
	Tax                   float64
	Amount                float64
	Snapshot              string
}

// DeleteWorkerParam represents the parameters for deleting site report worker
type DeleteWorkerParam struct {
	ID int64
}

// DeleteWorkersBySiteReportIDParam represents the parameters for deleting workers by site report ID
type DeleteWorkersBySiteReportIDParam struct {
	SiteReportID int64
}
