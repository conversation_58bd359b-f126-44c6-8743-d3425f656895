package sitereportworker

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	"gorm.io/gorm"
)

// createWorkerWithTx creates a new site report worker record within a transaction.
func (rsc SiteReportWorkerResource) createWorkerWithTx(ctx context.Context, tx *gorm.DB, param CreateWorkerParam) (int64, error) {
	db := tx.WithContext(ctx)

	// Convert time.Time to utils.LocalTime
	var breakTime *utils.LocalTime
	if param.BreakTime != nil {
		breakTime = &utils.LocalTime{Time: *param.BreakTime}
	}

	worker := SiteReportWorker{
		SiteReportID:          param.SiteReportID,
		UserID:                param.UserID,
		StartTime:             utils.LocalTime{Time: param.StartTime},
		EndTime:               utils.LocalTime{Time: param.EndTime},
		BreakTime:             breakTime,
		TransportationExpense: param.TransportationExpense,
		LeaderAllowance:       param.LeaderAllowance,
		DistantFeeID:          param.DistantFeeID,
		IncomeTaxID:           param.IncomeTaxID,
		Tax:                   param.Tax,
		Amount:                param.Amount,
		Snapshot:              param.Snapshot,
		Status:                param.Status,
		IssuedDate:            param.IssuedDate,
	}

	err := db.Create(&worker).Error
	if err != nil {
		return 0, log.LogError(err, nil)
	}

	return worker.ID, nil
}

// updateWorker updates an existing site report worker record.
func (rsc SiteReportWorkerResource) updateWorkerWithTx(ctx context.Context, tx *gorm.DB, param UpdateWorkerParam) error {
	db := tx.WithContext(ctx)

	updateFields := map[string]interface{}{
		"user_id":                param.UserID,
		"start_time":             param.StartTime,
		"end_time":               param.EndTime,
		"break_time":             param.BreakTime,
		"transportation_expense": param.TransportationExpense,
		"leader_allowance":       param.LeaderAllowance,
		"distant_fee_id":         param.DistantFeeID,
		"income_tax_id":          param.IncomeTaxID,
		"tax":                    param.Tax,
		"amount":                 param.Amount,
		"snapshot":               param.Snapshot,
	}

	err := db.Model(&SiteReportWorker{}).
		Where("id = ?", param.ID).
		Updates(updateFields).Error

	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// deleteWorker deletes a site report worker record.
func (rsc SiteReportWorkerResource) deleteWorkerWithTx(ctx context.Context, tx *gorm.DB, param DeleteWorkerParam) error {
	db := tx.WithContext(ctx)

	err := db.Delete(&SiteReportWorker{}, param.ID).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

// getBySiteReportID retrieves site report worker records by site report ID.
func (rsc SiteReportWorkerResource) getBySiteReportID(ctx context.Context, siteReportID int64) ([]SiteReportWorker, error) {
	var workers []SiteReportWorker

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("site_report_id = ?", siteReportID).
		Find(&workers).Error

	if err != nil {
		return []SiteReportWorker{}, log.LogError(err, nil)
	}

	return workers, nil
}
