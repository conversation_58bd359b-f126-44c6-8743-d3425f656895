package site_report

import (
	"errors"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	utils "github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/utils"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func validateGetList(req sitereport.GetListReq) error {
	if req.StartDate == "" {
		return ErrStartDateRequired
	}

	if req.EndDate == "" {
		return ErrEndDateRequired
	}

	// validate start date must be before end date
	if req.ParsedStartDate.After(req.ParsedEndDate) {
		return ErrStartDateAfterEndDate
	}

	return nil
}

func validateGetInvoiceList(req sitereport.GetInvoiceListReq) error {
	if req.StartDate == "" {
		return ErrStartDateRequired
	}

	if req.EndDate == "" {
		return ErrEndDateRequired
	}

	// validate start date must be before or equal to end date
	if req.ParsedStartDate.After(req.ParsedEndDate) {
		return ErrStartDateAfterEndDate
	}

	return nil
}

func validateBulkUpdate(req sitereport.BulkUpdateReq) error {
	// Validate site_report_ids is required and not empty
	if len(req.SiteReportIDs) == 0 {
		return ErrSiteReportIDsRequired
	}

	// Validate at least one field is provided
	if req.WorkDate == nil && req.IsLocked == nil && req.IsInvoiceIssued == nil {
		return ErrNoFieldsProvided
	}

	// Validate date format if work_date is provided
	if req.WorkDate != nil {
		_, err := time.Parse("2006-01-02", *req.WorkDate)
		if err != nil {
			return ErrInvalidDateFormat
		}
	}

	// Role-based authorization checks
	if req.WorkDate != nil {
		// work_date updates: Only RoleAdmin and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	if req.IsLocked != nil {
		if *req.IsLocked {
			// is_locked = true: Only RoleAdmin and RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		} else {
			// is_locked = false: Only RoleSuperAdmin
			if !utils.HasRole(req.UserRoles, constanta.RoleSuperAdmin) {
				return errors.New(constanta.Unauthorized)
			}
		}
	}

	if req.IsInvoiceIssued != nil && *req.IsInvoiceIssued {
		// is_invoice_issued = true: RoleSubAdmin, RoleAdmin, and RoleSuperAdmin
		if !utils.HasRole(req.UserRoles, constanta.RoleSubAdmin, constanta.RoleAdmin, constanta.RoleSuperAdmin) {
			return errors.New(constanta.Unauthorized)
		}
	}

	return nil
}

// parseAndValidateCalculationVariableRequest parses and validates time parameters
func parseAndValidateCalculationVariableRequest(req *sitereport.GetStatutoryCalculationVariableReq) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

func validateGetDetailList(req sitereport.GetDetailListReq) error {
	// Exactly one of work_date or id must be provided
	hasWorkDate := req.WorkDate != ""
	hasID := req.ID > 0

	if !hasWorkDate && !hasID {
		return ErrWorkDateOrIDRequired
	}

	if hasWorkDate && hasID {
		return ErrWorkDateAndIDMutuallyExclusive
	}

	return nil
}

// validateGetStatutoryCalculationVariable validates the calculation variable request
func validateGetStatutoryCalculationVariable(req sitereport.GetStatutoryCalculationVariableReq) error {
	if req.CustomerID <= 0 {
		return ErrCustomerIDRequired
	}

	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	return nil
}

// parseAndValidateWorkerCalculationRequest parses and validates time parameters for worker calculation
func parseAndValidateWorkerCalculationRequest(req *sitereport.WorkerCalculationParam) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateWorkerCalculation validates the worker calculation request
func validateWorkerCalculation(req sitereport.WorkerCalculationParam) error {
	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.WorkerID <= 0 {
		return ErrWorkerIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	// Validate transport expense is not negative
	if req.TransportExpense < 0 {
		return ErrNegativeTransportExpense
	}

	// Validate leader allowance is not negative
	if req.LeaderAllowance < 0 {
		return ErrNegativeLeaderAllowance
	}

	// Validate qualification allowance IDs if provided
	for _, id := range req.QualificationAllowanceIDs {
		if id <= 0 {
			return ErrInvalidQualificationID
		}
	}

	return nil
}

// parseAndValidateSaveWorkerRequest parses and validates time parameters for save worker request
func parseAndValidateSaveWorkerRequest(req *sitereport.SaveWorkerReq) error {
	// Parse start_time
	startTime, err := time.Parse(time.TimeOnly, req.StartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedStartTime = startTime

	// Parse end_time
	endTime, err := time.Parse(time.TimeOnly, req.EndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateSaveWorker validates the save worker request
func validateSaveWorker(req sitereport.SaveWorkerReq) error {
	if req.ID == 0 {
		return ErrIDRequired
	}

	if req.WorkerID <= 0 {
		return ErrWorkerIDRequired
	}

	if req.BasicPriceID <= 0 {
		return ErrBasicPriceIDRequired
	}

	if req.StartTime == "" {
		return ErrStartTimeRequired
	}

	if req.EndTime == "" {
		return ErrEndTimeRequired
	}

	// Validate start time is before end time
	if !req.ParsedStartTime.Before(req.ParsedEndTime) {
		return ErrStartTimeAfterEndTime
	}

	// Validate transport expense is not negative
	if req.TransportExpense < 0 {
		return ErrNegativeTransportExpense
	}

	// Validate leader allowance is not negative
	if req.LeaderAllowance < 0 {
		return ErrNegativeLeaderAllowance
	}

	// Validate qualification allowance IDs if provided
	for _, id := range req.QualificationAllowanceIDs {
		if id <= 0 {
			return ErrInvalidQualificationID
		}
	}

	return nil
}

// parseAndValidateCalculationRequest parses and validates time fields in the calculation request
func parseAndValidateCalculationRequest(req *sitereport.SiteReportCalculationReq) error {
	// Parse b_start_time
	startTime, err := time.Parse("15:04:05", req.BStartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedBStartTime = startTime

	// Parse b_end_time
	endTime, err := time.Parse("15:04:05", req.BEndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedBEndTime = endTime

	// Parse break_time if provided
	if req.BreakTime != nil {
		breakTime, err := time.Parse("15:04:05", *req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	return nil
}

// validateCalculation validates the calculation request parameters
func validateCalculation(req sitereport.SiteReportCalculationReq) error {
	// Validate required fields
	if req.DepartmentPicID <= 0 {
		return ErrInvalidDepartmentPicID
	}

	if req.BasicPriceID <= 0 {
		return ErrInvalidBasicPriceID
	}

	if req.TotalWorker <= 0 {
		return ErrInvalidTotalWorker
	}

	if req.DistrictBlockID <= 0 {
		return ErrInvalidDistrictBlockID
	}

	if req.DistrictBlockUnit <= 0 {
		return ErrInvalidDistrictBlockUnit
	}

	// Validate optional fields
	if req.DailyReportAdditionID != nil && *req.DailyReportAdditionID <= 0 {
		return ErrInvalidDailyReportAdditionID
	}

	if req.TransitPlaceBlockID != nil && *req.TransitPlaceBlockID <= 0 {
		return ErrInvalidTransitPlaceBlockID
	}

	if req.TransitPlaceBlockUnit != nil && *req.TransitPlaceBlockUnit <= 0 {
		return ErrInvalidTransitPlaceBlockUnit
	}

	if req.LateEarlyWorker != nil && *req.LateEarlyWorker < 0 {
		return ErrInvalidLateEarlyWorker
	}

	// Validate time range
	if req.ParsedBEndTime.Before(req.ParsedBStartTime) {
		return ErrInvalidTimeRange
	}

	// Validate extra time charge items
	for _, item := range req.ExtraTimeCharge {
		if item.TotalWorker <= 0 {
			return ErrInvalidExtraTimeChargeWorker
		}
		// Validate time format
		_, err := time.Parse("15:04:05", item.Time)
		if err != nil {
			return ErrInvalidTimeFormat
		}
	}

	// Validate option IDs
	for _, option := range req.Option {
		if option.OptionID <= 0 {
			return ErrInvalidOptionID
		}
	}

	return nil
}

// parseAndValidateSaveSiteReportRequest parses and validates time fields in SaveSiteReportReq
func parseAndValidateSaveSiteReportRequest(req *sitereport.SaveSiteReportReq) error {
	// Skip validation for delete operation
	if req.ID < 0 {
		return nil
	}

	// Parse work_date
	workDate, err := time.Parse(time.DateOnly, req.WorkDate)
	if err != nil {
		return ErrInvalidDateFormat
	}
	req.ParsedWorkDate = workDate

	// Parse bill_date
	billDate, err := time.Parse(time.DateOnly, req.BillDate)
	if err != nil {
		return ErrInvalidDateFormat
	}
	req.ParsedBillDate = billDate

	// Parse b_start_time
	bStartTime, err := time.Parse(time.TimeOnly, req.BStartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedBStartTime = bStartTime

	// Parse b_end_time
	bEndTime, err := time.Parse(time.TimeOnly, req.BEndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedBEndTime = bEndTime

	// Parse s_start_time
	sStartTime, err := time.Parse(time.TimeOnly, req.SStartTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedSStartTime = sStartTime

	// Parse s_end_time
	sEndTime, err := time.Parse(time.TimeOnly, req.SEndTime)
	if err != nil {
		return ErrInvalidTimeFormat
	}
	req.ParsedSEndTime = sEndTime

	// Parse break_time (optional)
	if req.BreakTime != "" {
		breakTime, err := time.Parse(time.TimeOnly, req.BreakTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ParsedBreakTime = &breakTime
	}

	// Parse extra time charge times
	for i := range req.ExtraTimeCharge {
		extraTime, err := time.Parse(time.TimeOnly, req.ExtraTimeCharge[i].Time)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.ExtraTimeCharge[i].ParsedTime = extraTime
	}

	// Parse worker times
	for i := range req.Worker {
		// Parse start_time
		startTime, err := time.Parse(time.TimeOnly, req.Worker[i].StartTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.Worker[i].ParsedStartTime = startTime

		// Parse end_time
		endTime, err := time.Parse(time.TimeOnly, req.Worker[i].EndTime)
		if err != nil {
			return ErrInvalidTimeFormat
		}
		req.Worker[i].ParsedEndTime = endTime

		// Parse break_time (optional)
		if req.Worker[i].BreakTime != "" {
			breakTime, err := time.Parse(time.TimeOnly, req.Worker[i].BreakTime)
			if err != nil {
				return ErrInvalidTimeFormat
			}
			req.Worker[i].ParsedBreakTime = &breakTime
		}
	}

	return nil
}

// validateSaveSiteReport validates the save site report request
func validateSaveSiteReport(req sitereport.SaveSiteReportReq) error {
	// Skip validation for delete operation
	if req.ID < 0 {
		return nil
	}

	// Validate work_date is required
	if req.WorkDate == "" {
		return ErrStartDateRequired
	}

	// Validate site_name is required
	if req.SiteName == "" {
		return ErrSiteNameRequired
	}

	// Validate department_pic_id
	if req.DepartmentPicID <= 0 {
		return ErrInvalidDepartmentPicID
	}

	// Validate bill_date is required
	if req.BillDate == "" {
		return ErrBillDateRequired
	}

	// Validate basic_price_id
	if req.BasicPriceID <= 0 {
		return ErrInvalidBasicPriceID
	}

	// Validate total_worker
	if req.TotalWorker <= 0 {
		return ErrInvalidTotalWorker
	}

	// Validate district_block_id
	if req.DistrictBlockID <= 0 {
		return ErrInvalidDistrictBlockID
	}

	// Validate district_block_unit
	if req.DistrictBlockUnit <= 0 {
		return ErrInvalidDistrictBlockUnit
	}

	// Validate optional fields
	if req.DailyReportAdditionID != nil && *req.DailyReportAdditionID <= 0 {
		return ErrInvalidDailyReportAdditionID
	}

	if req.TransitPlaceBlockID != nil && *req.TransitPlaceBlockID <= 0 {
		return ErrInvalidTransitPlaceBlockID
	}

	if req.TransitPlaceBlockUnit != nil && *req.TransitPlaceBlockUnit <= 0 {
		return ErrInvalidTransitPlaceBlockUnit
	}

	// Validate late_early_worker is not negative
	if req.LateEarlyWorker < 0 {
		return ErrInvalidLateEarlyWorker
	}

	// Validate time ranges
	if req.ParsedBEndTime.Before(req.ParsedBStartTime) {
		return ErrInvalidTimeRange
	}

	if req.ParsedSEndTime.Before(req.ParsedSStartTime) {
		return ErrInvalidTimeRange
	}

	// Validate extra time charge items
	for _, item := range req.ExtraTimeCharge {
		if item.TotalWorker <= 0 {
			return ErrInvalidExtraTimeChargeWorker
		}
	}

	// Validate option IDs
	for _, option := range req.Option {
		if option.OptionID <= 0 {
			return ErrInvalidOptionID
		}
	}

	// Validate worker data
	for _, worker := range req.Worker {
		if worker.WorkerID <= 0 {
			return ErrWorkerIDRequired
		}

		if worker.StartTime == "" {
			return ErrStartTimeRequired
		}

		if worker.EndTime == "" {
			return ErrEndTimeRequired
		}

		// Validate start time is before end time
		if !worker.ParsedStartTime.Before(worker.ParsedEndTime) {
			return ErrStartTimeAfterEndTime
		}

		// Validate transport expense is not negative
		if worker.TransportExpense < 0 {
			return ErrNegativeTransportExpense
		}

		// Validate leader allowance is not negative
		if worker.LeaderAllowance < 0 {
			return ErrNegativeLeaderAllowance
		}

		// Validate qualification IDs
		for _, qual := range worker.Qualifications {
			if qual.QualificationID <= 0 {
				return ErrInvalidQualificationID
			}
		}
	}

	return nil
}
