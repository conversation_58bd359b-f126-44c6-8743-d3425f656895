package site_report

import (
	"net/http"
	"time"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
	"github.com/labstack/echo/v4"
)

func GetList(c echo.Context) error {
	var status int

	var req sitereport.GetListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	startDate, err := time.Parse(time.DateOnly, req.StartDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedStartDate = startDate

	endDate, err := time.Parse(time.DateOnly, req.EndDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedEndDate = endDate

	err = validateGetList(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func GetInvoiceList(c echo.Context) error {
	var status int

	var req sitereport.GetInvoiceListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	startDate, err := time.Parse(time.DateOnly, req.StartDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedStartDate = startDate

	endDate, err := time.Parse(time.DateOnly, req.EndDate)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: ErrInvalidDateFormat.Error()})
	}
	req.ParsedEndDate = endDate

	err = validateGetInvoiceList(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetInvoiceList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetInvoiceList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func BulkUpdate(c echo.Context) error {
	var status int

	var req sitereport.BulkUpdateReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Get user roles from JWT context
	userRoles, ok := c.Get("roles").([]string)
	if !ok {
		status = http.StatusUnauthorized
		return c.JSON(status, types.BasicResp{Message: constanta.Unauthorized})
	}

	req.UserRoles = userRoles
	err = validateBulkUpdate(req)
	if err != nil {
		if err.Error() == constanta.Unauthorized {
			status = http.StatusUnauthorized
			return c.JSON(status, types.BasicResp{Message: err.Error()})
		}
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	err = server.SiteReportUseCase.BulkUpdate(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.BulkUpdate]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func GetStatutoryCalculationVariable(c echo.Context) error {
	var status int

	var req sitereport.GetStatutoryCalculationVariableReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate time parameters
	err = parseAndValidateCalculationVariableRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateGetStatutoryCalculationVariable(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetStatutoryCalculationVariable(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetStatutoryCalculationVariable]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func WorkerCalculation(c echo.Context) error {
	var status int

	var req sitereport.WorkerCalculationParam
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate time parameters
	err = parseAndValidateWorkerCalculationRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateWorkerCalculation(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.WorkerCalculation(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.WorkerCalculation]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func SaveWorker(c echo.Context) error {
	var status int

	var req sitereport.SaveWorkerReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate time parameters
	err = parseAndValidateSaveWorkerRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateSaveWorker(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	err = server.SiteReportUseCase.SaveWorker(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.SaveWorker]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func GetDetailList(c echo.Context) error {
	var status int

	var req sitereport.GetDetailListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse work_date if provided
	if req.WorkDate != "" {
		workDate, err := time.Parse(time.DateOnly, req.WorkDate)
		if err != nil {
			status = http.StatusBadRequest
			return c.JSON(status, types.BasicResp{Message: ErrInvalidWorkDateFormat.Error()})
		}
		req.ParsedWorkDate = workDate
	}

	// Validate request parameters
	err = validateGetDetailList(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.GetDetailList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.GetDetailList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func Calculation(c echo.Context) error {
	var status int

	var req sitereport.SiteReportCalculationReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate time parameters
	err = parseAndValidateCalculationRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateCalculation(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.SiteReportUseCase.SiteReportCalculation(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.Calculation]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func SaveSiteReport(c echo.Context) error {
	var status int

	var req sitereport.SaveSiteReportReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Parse and validate request parameters
	err = parseAndValidateSaveSiteReportRequest(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Validate request parameters
	err = validateSaveSiteReport(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	// Get user ID from JWT token for audit fields
	userID, ok := c.Get("id").(int64)
	if !ok {
		status = http.StatusUnauthorized
		return c.JSON(status, types.BasicResp{Message: constanta.Unauthorized})
	}
	req.UserID = userID

	err = server.SiteReportUseCase.SaveSiteReport(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
	}
	status = http.StatusOK
	log.LogInfo("[site_report.SaveSiteReport]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}
